import * as React from "react"
import { AnimatePresence } from "motion/react"
import BasicToast, { type ToastType } from "@/components/customize/Toast/BasicToast"
import { useToast, type ToasterToast } from "./use-toast"

/**
 * Toaster-Komponente für Benachrichtigungen
 * Zeigt Toast-Benachrichtigungen mit der BasicToast-Komponente an
 */
const Toaster: React.FC = () => {
  const { toasts, dismiss } = useToast()

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
      <AnimatePresence>
        {toasts.map(({ id, title, description, type }: ToasterToast) => {
          // Erstelle die Nachricht aus title und description
          const message = title && description
            ? `${title}: ${description}`
            : title || description || "Benachrichtigung"

          return (
            <BasicToast
              key={id}
              message={message}
              type={type as ToastType}
              duration={5000}
              onClose={() => {
                dismiss(id); // Toast korrekt aus dem State entfernen
              }}
            />
          )
        })}
      </AnimatePresence>
    </div>
  )
}

// Standard-Export für die Toaster-Komponente
export default Toaster;
