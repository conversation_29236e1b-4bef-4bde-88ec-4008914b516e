import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Save, AlertTriangle, MessageSquare, X, Mail } from 'lucide-react';
import { StoerungCreateData, StoerungStatus } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import stoerungKategorienService from '@/services/stoerung-kategorien.service';
import bereitschaftsAssignmentService, { BereitschaftsAssignmentData, EmailTemplate } from '@/services/bereitschafts-assignment.service';
import { useToast } from '@/components/ui/use-toast';
import FileUpload from '@/components/Animation/kokonutui/file-upload-stoerung';

interface StoerungsFormProps {
  open?: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onOpenChange?: (open: boolean) => void;
}

export const StoerungsForm: React.FC<StoerungsFormProps> = ({ open = true, onClose, onSubmit, onOpenChange }) => {
  const [formData, setFormData] = useState<StoerungCreateData>({
    title: '',
    description: '',
    severity: 'MEDIUM',
    status: StoerungStatus.NEU,
    category: '',
    affected_system: '',
    location: '',
    reported_by: '',
    assigned_to: '',
    tags: [],
    send_protocol: false,
  });

  // State für Bild-Uploads
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);

  const [sendProtocol, setSendProtocol] = useState(false);
  const [sendToTeams, setSendToTeams] = useState(true); // Teams standardmäßig aktiviert
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [teamsStatus, setTeamsStatus] = useState<string | null>(null);
  
  // Bereitschafts-Zuweisung States
  const [bereitschaftsperson, setBereitschaftsperson] = useState<BereitschaftsAssignmentData | null>(null);
  const [emailTemplate, setEmailTemplate] = useState<EmailTemplate | null>(null);
  const [autoAssignmentEnabled, setAutoAssignmentEnabled] = useState(true);
  const [bereitschaftsStatus, setBereitschaftsStatus] = useState<string | null>(null);

  // Toast für Erfolgsmeldungen
  const { toast } = useToast();

  // Hilfsfunktion für Toast-Nachrichten
  const showToast = (title: string, description: string, options?: { type?: 'success' | 'error' | 'warning' | 'info'; duration?: number }) => {
    toast({
      title,
      description,
      type: options?.type || 'info'
    });
  };

  // Ref für Component Lifecycle Tracking
  const isMountedRef = useRef(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Neue State-Variablen für database-driven categories
  const [kategorien, setKategorien] = useState<any[]>([]);
  const [kategorienLoading, setKategorienLoading] = useState(true);
  const [kategorienError, setKategorienError] = useState<string | null>(null);

  const severityOptions = [
    { value: 'LOW', label: 'Niedrig', color: 'text-green-600' },
    { value: 'MEDIUM', label: 'Mittel', color: 'text-yellow-600' },
    { value: 'HIGH', label: 'Hoch', color: 'text-orange-600' },
    { value: 'CRITICAL', label: 'Kritisch', color: 'text-red-600' },
  ];

  const categoryOptions = kategorien.map(k => k.name);
  const [affectedSystemOptions, setAffectedSystemOptions] = useState<string[]>([]);

  const locationOptions = [
    'Ludwigsburg',
    'Stuttgart',
    'Hannover',
    'Illingen',
  ];

  // Cleanup beim Unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Sichere State-Update Funktion
  const safeSetState = (setter: Function, value: any) => {
    if (isMountedRef.current) {
      setter(value);
    }
  };

  // Lade Kategorien und Bereitschaftsperson beim ersten Rendern
  useEffect(() => {
    const loadKategorien = async () => {
      const timeout = setTimeout(() => {
        if (isMountedRef.current) {
          const fallbackData = [
            { name: 'Systeme', systeme: [{ name: 'SAP' }, { name: 'ITM' }, { name: 'Wamas' }, { name: 'Mail' }, { name: 'Citrix' }, { name: 'MFR' }] },
            { name: 'Infrastruktur', systeme: [{ name: 'Netzwerk' }, { name: 'WLAN' }] },
            { name: 'Technische Komponenten', systeme: [{ name: 'Förderband' }, { name: 'FTS' }, { name: 'RBG' }, { name: 'Greifer' }, { name: 'Schrumpfanlage' }, { name: 'Ablängmaschinen' }] },
            { name: 'Kommissionierung', systeme: [{ name: 'Automatisches Trommellager (ATrL)' }, { name: 'Automatisches Ringlager (ARiL)' }, { name: 'Schäfer Karussell (SCS)' }] },
            { name: 'Hardware', systeme: [{ name: 'Mobile Drucker' }, { name: 'Label Drucker' }, { name: 'Lieferschein Drucker' }, { name: 'Terminal' }, { name: 'PC' }] },
          ];
          setKategorien(fallbackData);
          setKategorienLoading(false);
          safeSetState(setKategorienError, 'Timeout - verwende Fallback-Daten');
        }
      }, 5000);

      try {
        setKategorienLoading(true);
        const data = await stoerungKategorienService.getKategorien();
        clearTimeout(timeout);
        setKategorien(data);
        safeSetState(setKategorienError, null);
      } catch (err) {
        clearTimeout(timeout);
        console.error('Fehler beim Laden der Kategorien:', err);
        safeSetState(setKategorienError, 'Fehler beim Laden der Kategorien');

        const fallbackData = [
          { name: 'Systeme', systeme: [{ name: 'SAP' }, { name: 'ITM' }, { name: 'Wamas' }, { name: 'Mail' }, { name: 'Citrix' }, { name: 'MFR' }] },
          { name: 'Infrastruktur', systeme: [{ name: 'Netzwerk' }, { name: 'WLAN' }] },
          { name: 'Technische Komponenten', systeme: [{ name: 'Förderband' }, { name: 'FTS' }, { name: 'RBG' }, { name: 'Greifer' }, { name: 'Schrumpfanlage' }, { name: 'Ablängmaschinen' }] },
          { name: 'Kommissionierung', systeme: [{ name: 'Automatisches Trommellager (ATrL)' }, { name: 'Automatisches Ringlager (ARiL)' }, { name: 'Schäfer Karussell (SCS)' }] },
          { name: 'Hardware', systeme: [{ name: 'Mobile Drucker' }, { name: 'Label Drucker' }, { name: 'Lieferschein Drucker' }, { name: 'Terminal' }, { name: 'PC' }] },
        ];
        setKategorien(fallbackData);
      } finally {
        setKategorienLoading(false);
      }
    };

    const loadBereitschaftsperson = async () => {
      if (!autoAssignmentEnabled) return;
      
      try {
        safeSetState(setBereitschaftsStatus, 'Lade aktuelle Bereitschaftsperson...');
        const validation = await bereitschaftsAssignmentService.validateAutoAssignment();
        
        if (validation.isValid && validation.assignmentData) {
          setBereitschaftsperson(validation.assignmentData);
          if (isMountedRef.current) {
            setFormData(prev => ({
              ...prev,
              assigned_to: validation.assignmentData!.name
            }));
          }
          safeSetState(setBereitschaftsStatus, `Automatisch zugewiesen an: ${validation.assignmentData.name}`);
        } else {
          safeSetState(setBereitschaftsStatus, `${validation.message}`);
        }
      } catch (error) {
        console.error('Fehler beim Laden der Bereitschaftsperson:', error);
        safeSetState(setBereitschaftsStatus, 'Fehler beim Laden der Bereitschaftsperson');
      }
    };

    loadKategorien();
    loadBereitschaftsperson();
  }, []);

  useEffect(() => {
    if (!autoAssignmentEnabled) {
      safeSetState(setBereitschaftsperson, null);
      safeSetState(setBereitschaftsStatus, null);
      if (isMountedRef.current) {
        setFormData(prev => ({ ...prev, assigned_to: '' }));
      }
    }
  }, [autoAssignmentEnabled]);

  const handleInputChange = (field: keyof StoerungCreateData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    if (field === 'category') {
      // Finde die ausgewählte Kategorie und setze die verfügbaren Systeme
      const selectedKategorie = kategorien.find(k => k.name === value);
      console.log('📝 Kategorie ausgewählt:', value, 'Gefundene Kategorie:', selectedKategorie);
      if (selectedKategorie?.systeme) {
        // Systeme können entweder Objekte mit .name Property oder direkte Strings sein
        const systemeNames = selectedKategorie.systeme.map((s: any) =>
          typeof s === 'string' ? s : s.name
        );
        console.log('📦 Systeme für Kategorie:', systemeNames);
        setAffectedSystemOptions(systemeNames);
      } else {
        setAffectedSystemOptions([]);
      }
      if (isMountedRef.current) {
        setFormData(prev => ({ ...prev, affected_system: '' }));
      }
    }
  };

  const handleProtocolChange = (checked: boolean) => {
    setSendProtocol(checked);
  };

  const handleTeamsChange = (checked: boolean) => {
    setSendToTeams(checked);
  };

  // E-Mail Template generieren
  const generateEmailTemplate = async () => {
    if (!bereitschaftsperson) return null;
    
    try {
      console.log('📧 Generiere E-Mail Template für:', bereitschaftsperson.name);
      const template = await bereitschaftsAssignmentService.createEmailTemplate(
        bereitschaftsperson,
        formData
      );
      console.log('✉️ E-Mail Template erstellt:', template);
      setEmailTemplate(template);
      return template; // 🎉 Wichtig: Template direkt zurückgeben
    } catch (error) {
      console.error('Fehler beim Generieren des E-Mail Templates:', error);
      return null;
    }
  };

  // E-Mail in Outlook öffnen (visuell, nicht automatisch versenden)
  const openEmailInOutlook = (template: EmailTemplate) => {
    try {
      console.log('📧 Öffne Outlook mit vorbefüllter E-Mail:', template.to);
      
      // URL-encode die E-Mail-Parameter für Outlook
      const subject = encodeURIComponent(template.subject);
      const body = encodeURIComponent(template.body);
      const to = encodeURIComponent(template.to);
      
      // Erstelle mailto-Link für Outlook - öffnet Outlook mit vorbefüllter E-Mail
      const mailtoLink = `mailto:${to}?subject=${subject}&body=${body}`;
      
      // Öffne Outlook mit der vorbereiteten E-Mail (User kann selbst entscheiden ob senden)
      if (window.electronAPI?.shell) {
        window.electronAPI.shell.openExternal(mailtoLink);
        console.log('✅ Outlook wurde geöffnet mit vorbefüllter E-Mail');
      } else {
        // Fallback für Browser-Umgebung
        window.open(mailtoLink, '_blank');
        console.log('✅ Browser-Fallback: mailto-Link geöffnet');
      }
      
      // Toast-Meldung auch anzeigen wenn Component unmounted ist
      showToast(
        'Outlook geöffnet',
        'Outlook wurde mit einer vorbefüllten E-Mail geöffnet. Sie können die E-Mail prüfen und selbst versenden.',
        { type: 'success', duration: 5000 }
      );
    } catch (error) {
      console.error('❌ Fehler beim Öffnen von Outlook:', error);
      showToast(
        'Fehler',
        'Outlook konnte nicht geöffnet werden. Bitte prüfen Sie, ob Outlook installiert ist.',
        { type: 'error', duration: 4000 }
      );
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      setError('Titel ist erforderlich');
      return;
    }

    try {
      safeSetState(setLoading, true);
      safeSetState(setError, null);
      safeSetState(setTeamsStatus, null);

      // E-Mail Template vor dem Speichern generieren
      console.log('📎 Debug vor E-Mail Template:', { bereitschaftsperson, autoAssignmentEnabled });
      let generatedEmailTemplate = null;
      if (bereitschaftsperson && autoAssignmentEnabled) {
        console.log('🚀 Generiere E-Mail Template...');
        generatedEmailTemplate = await generateEmailTemplate();
      } else {
        console.log('⚠️ Kein E-Mail Template - Bereitschaftsperson oder AutoAssignment fehlt');
      }

      const submitData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description?.trim() || undefined,
        category: formData.category?.trim() || undefined,
        affected_system: formData.affected_system?.trim() || undefined,
        location: formData.location?.trim() || undefined,
        reported_by: formData.reported_by?.trim() || undefined,
        assigned_to: formData.assigned_to?.trim() || undefined,
        send_protocol: sendProtocol,
      };

      // Störung in der Datenbank erstellen (mit automatischem E-Mail-Versand wenn aktiviert)
      const createdStoerung = await stoerungenService.createStoerung(submitData);

      // Prüfe, ob E-Mail erfolgreich versendet wurde (Backend-Response)
      const emailSent = (createdStoerung as any)?.emailSent || false;
      if (sendProtocol && emailSent && isMountedRef.current) {
        showToast(
          'E-Mail versendet',
          `Störungs-E-Mail wurde erfolgreich an ${formData.assigned_to} gesendet.`,
          { type: 'success', duration: 4000 }
        );
      } else if (sendProtocol && !emailSent && isMountedRef.current) {
        showToast(
          'E-Mail-Versand fehlgeschlagen',
          'Die Störung wurde erstellt, aber die E-Mail konnte nicht versendet werden. Bitte überprüfen Sie die E-Mail-Konfiguration.',
          { type: 'warning', duration: 6000 }
        );
      }

      // Bilder hochladen, falls vorhanden
      if (uploadedImages.length > 0 && createdStoerung?.id) {
        try {
          for (const image of uploadedImages) {
            const formData = new FormData();
            formData.append('image', image);
            formData.append('uploaded_by', 'system'); // oder aktueller Benutzer

            const response = await fetch(`/api/stoerungen/${createdStoerung.id}/upload-image`, {
              method: 'POST',
              body: formData,
            });

            if (!response.ok) {
              console.error(`Fehler beim Hochladen von ${image.name}:`, await response.text());
            }
          }

          // Zeige Toast-Erfolgsmeldung mit Bildern (ohne isMountedRef Check)
          console.log('🎉 Zeige Toast-Meldung für erstellte Störung mit Bildern');
          showToast(
            'Störung und Bilder erstellt',
            `Die neue Störung wurde erfolgreich angelegt und ${uploadedImages.length} Bild(er) hochgeladen${bereitschaftsperson ? `. Zugewiesen an ${bereitschaftsperson.name}` : ''}.`,
            { type: 'success', duration: 4000 }
          );
        } catch (imageError) {
          console.error('Fehler beim Hochladen der Bilder:', imageError);
          // Zeige Toast-Erfolgsmeldung mit Bildern-Upload-Fehler (ohne isMountedRef Check)
          console.log('⚠️ Zeige Toast-Meldung für Bilder-Upload-Fehler');
          showToast(
            'Störung erstellt, Bilder-Upload fehlgeschlagen',
            `Die Störung wurde erstellt, aber beim Hochladen der Bilder ist ein Fehler aufgetreten.`,
            { type: 'warning', duration: 5000 }
          );
        }
      } else {
        // Zeige Toast-Erfolgsmeldung ohne Bilder (ohne isMountedRef Check)
        console.log('🎉 Zeige Toast-Meldung für erstellte Störung');
        showToast(
          'Störung erstellt',
          `Die neue Störung wurde erfolgreich angelegt${bereitschaftsperson ? ` und an ${bereitschaftsperson.name} zugewiesen` : ''}.`,
          { type: 'success', duration: 4000 }
        );
      }

      // An Microsoft Teams senden, falls aktiviert
      if (sendToTeams && window.electronAPI?.teams) {
        try {
          safeSetState(setTeamsStatus, 'Sende an Microsoft Teams...');
          // Prepare Teams data with proper typing
          const teamsData = {
            title: submitData.title,
            severity: submitData.severity,
            status: submitData.status || StoerungStatus.NEU, // Provide default value if undefined
            send_protocol: sendProtocol,
            ...(submitData.description && { description: submitData.description }),
            ...(submitData.category && { category: submitData.category }),
            ...(submitData.affected_system && { affected_system: submitData.affected_system }),
            ...(submitData.location && { location: submitData.location }),
            ...(submitData.reported_by && { reported_by: submitData.reported_by }),
          };

          const teamsResult = await window.electronAPI.teams.sendStoerung(teamsData);

          if (teamsResult.success) {
            safeSetState(setTeamsStatus, '✅ Erfolgreich an Teams gesendet');
          } else {
            safeSetState(setTeamsStatus, `⚠️ Teams-Fehler: ${teamsResult.error}`);
            console.warn('Teams sending failed:', teamsResult.error);
          }
        } catch (teamsError) {
          console.error('Teams integration error:', teamsError);
          safeSetState(setTeamsStatus, '⚠️ Teams-Integration nicht verfügbar');
        }
      }

      // Automatisch Outlook mit E-Mail öffnen, falls E-Mail Template vorhanden
      console.log('👀 Prüfe E-Mail Template:', generatedEmailTemplate);
      if (generatedEmailTemplate) {
        console.log('🚀 Öffne Outlook mit E-Mail Template');
        openEmailInOutlook(generatedEmailTemplate);
      } else {
        console.log('⚠️ Kein E-Mail Template verfügbar');
      }

      // 🎉 WICHTIG: Modal erst NACH Toast-Anzeige und Outlook-Start schließen
      setTimeout(() => {
        console.log('🚀 Schließe Modal nach erfolgreichem Submit');
        if (onOpenChange) {
          onOpenChange(false); // Korrekte Modal-Schließung
        }
        onSubmit(); // Callback für Parent
      }, 2000); // 2 Sekunden Verzögerung für beide Toast-Meldungen

    } catch (err) {
      console.error('Error creating störung:', err);
      safeSetState(setError, 'Fehler beim Erstellen der Störung');
    } finally {
      safeSetState(setLoading, false);
    }
  };

  // ⚠️ WICHTIG: Bedingte Rückgabe NACH allen Hook-Aufrufen um "Expected static flag was missing" zu vermeiden
  if (!open) return null;

  return (
    <div className="w-full max-w-md mx-auto">
        <div className="flex items-center gap-2 mb-6">
          <AlertTriangle className="h-6 w-6 text-red-600" />
          <h2 className="text-2xl font-bold">Neue Störung erfassen</h2>
        </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title">Titel *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Kurze Beschreibung der Störung"
            required
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Beschreibung</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Detaillierte Beschreibung der Störung"
            rows={3}
          />
        </div>

        {/* Zwei-Spalten Layout für Eingabefelder und Datei-Upload */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Linke Spalte: Eingabefelder */}
          <div className="space-y-4">
            {/* Severity */}
            <div className="space-y-2">
              <Label htmlFor="severity">Schweregrad</Label>
              <Select value={formData.severity} onValueChange={(value) => handleInputChange('severity', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {severityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className={option.color}>{option.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">Kategorie</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange('category', value)}
                disabled={kategorienLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder={kategorienLoading ? "Lade Kategorien..." : "Kategorie wählen"} />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {kategorienLoading && (
                <p className="text-xs text-gray-500">Lade Kategorien...</p>
              )}
            </div>

            {/* Affected System */}
            <div className="space-y-2">
              <Label htmlFor="affected_system">Betroffenes System</Label>
              <Select
                value={formData.affected_system}
                onValueChange={(value) => handleInputChange('affected_system', value)}
                disabled={!formData.category || kategorienLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder={
                    !formData.category
                      ? "Zuerst Kategorie wählen"
                      : kategorienLoading
                      ? "Lade Systeme..."
                      : "System wählen"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {affectedSystemOptions.map((system) => (
                    <SelectItem key={system} value={system}>
                      {system}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!formData.category && (
                <p className="text-xs text-gray-500">Wählen Sie zuerst eine Kategorie aus</p>
              )}
            </div>

            {/* Location */}
            <div className="space-y-2">
              <Label htmlFor="location">Standort</Label>
              <Select
                value={formData.location}
                onValueChange={(value) => handleInputChange('location', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Standort wählen" />
                </SelectTrigger>
                <SelectContent>
                  {locationOptions.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Rechte Spalte: Datei-Upload */}
          <div className="space-y-2">
            <Label>Dateien anhängen</Label>
            <div className="space-y-2">
              {uploadedImages.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                  <span className="text-sm text-gray-700">{file.name}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setUploadedImages(prev => prev.filter((_, i) => i !== index))}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              {uploadedImages.length < 5 && (
                <FileUpload
                  onUploadSuccess={(file: File) => {
                    setUploadedImages(prev => [...prev, file]);
                  }}
                  onUploadError={(error: { message: string; code: string }) => {
                    console.error('Upload error:', error);
                    setError(`Datei-Upload Fehler: ${error.message}`);
                  }}
                  acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}
                  maxFileSize={5 * 1024 * 1024} // 5MB
                  uploadDelay={0} // Kein Upload-Delay, da wir nur die Datei sammeln
                  className="w-full"
                />
              )}
            </div>
            <p className="text-sm text-gray-600">
              Sie können bis zu 5 Bilder hochladen (max. 5MB pro Bild). Unterstützte Formate: JPG, PNG, GIF, WebP
            </p>
          </div>
        </div>
        {/* Reported by */}

        {/* Reported by */}
        <div className="space-y-2">
          <Label htmlFor="reported_by">Gemeldet von</Label>
          <Input
            id="reported_by"
            value={formData.reported_by}
            onChange={(e) => handleInputChange('reported_by', e.target.value)}
            placeholder="Name des Melders"
          />
        </div>



        {/* E-Mail-Vorschau wurde entfernt - Outlook öffnet automatisch beim Absenden */}

        {/* Störungsprotokoll versenden */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-protocol"
              checked={sendProtocol}
              onCheckedChange={handleProtocolChange}
            />
            <Label
              htmlFor="send-protocol"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Störungsprotokoll versenden
            </Label>
          </div>
          <p className="text-sm text-gray-600">
            Aktivieren Sie diese Option, um automatisch ein Störungsprotokoll per E-Mail zu versenden.
          </p>
        </div>

        {/* An Microsoft Teams senden */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-to-teams"
              checked={sendToTeams}
              onCheckedChange={handleTeamsChange}
            />
            <Label
              htmlFor="send-to-teams"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              An Microsoft Teams senden
            </Label>
          </div>
          <p className="text-sm text-gray-600">
            Sendet eine Benachrichtigung über die neue Störung an den konfigurierten Teams-Kanal.
          </p>
          {teamsStatus && (
            <p className={`text-sm ${teamsStatus.includes('✅') ? 'text-green-600' : 'text-orange-600'}`}>
              {teamsStatus}
            </p>
          )}
        </div>

        {/* Submit buttons */}
        <div className="flex gap-2 pt-4">
          <Button type="submit" variant="accept" disabled={loading} className="">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Speichere...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Störung erfassen
              </>
            )}
          </Button>

          <Button type="button" variant="ghost" onClick={onClose}>
            Abbrechen
          </Button>

          {/* Teams Test Button */}
          {sendToTeams && window.electronAPI?.teams && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  safeSetState(setTeamsStatus, 'Teste Teams-Verbindung...');
                  const result = await window.electronAPI.teams.testConnection();
                  if (result.success) {
                    safeSetState(setTeamsStatus, '✅ Teams-Verbindung erfolgreich');
                  } else {
                    safeSetState(setTeamsStatus, `❌ Teams-Test fehlgeschlagen: ${result.error}`);
                  }
                } catch (error) {
                  safeSetState(setTeamsStatus, '❌ Teams nicht verfügbar');
                }
              }}
              disabled={loading}
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Teams testen
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};